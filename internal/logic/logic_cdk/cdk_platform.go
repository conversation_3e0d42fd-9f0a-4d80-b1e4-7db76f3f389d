package logic_cdk

import (
	"context"
	"errors"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"hallsrv/internal/dao/dao_cdk"
	"hallsrv/internal/model/model_cdk"
)

// CreateCDKBatchWithCodes 创建CDK批次和对应的CDK码
func CreateCDKBatchWithCodes(ctx context.Context, input *gmPB.GmCmdCreateCDKReq) error {
	output := &gmPB.GmCmdCreateCDKRsp{}
	entry := logx.NewLogEntry(ctx)
	entry.Infof("input: %+v", input)

	// 输入参数校验
	if input.StartTime >= input.EndTime {
		return errors.New("end_time must be greater than start_time")
	}
	if len(input.Rewards) == 0 {
		return  errors.New("rewards list cannot be empty")
	}
	if input.GenerationCount > 100000 {
		return errors.New("generation_count cannot be greater than 100000")
	}

	// 生成CDK
	var cdkCodesToCreate []string
	switch input.GenerationOption {
	case commonPB.CDK_GENERATION_OPTION_CGO_RANDOM:
		if input.GenerationCount <= 0 {
			return errors.New("generation_count must be > 0 for random generation")
		}
		var genErr error
		cdkCodesToCreate, genErr = random.NewCDKeyGenerator(12).GenerateBatch(int(input.GenerationCount))
		if genErr != nil {
			return fmt.Errorf("failed to generate CDK codes: %s", genErr)
		}
	case commonPB.CDK_GENERATION_OPTION_CGO_CUSTOM:
		cdkCodesToCreate = input.ManualCdks
	default:
		return fmt.Errorf("invalid GenerationOption: %d", input.GenerationOption)
	}
	if len(cdkCodesToCreate) == 0 {
		return errors.New("no CDK codes to create (count is zero or manual list is empty)")
	}

	// 创建CDKBatch对象
	cdkBatch := model_cdk.ToCDKBatch(input)
	cdkBatch.CDKCount = int32(len(cdkCodesToCreate))
	cdkRecords := model_cdk.CreateCDKRecords(cdkCodesToCreate)

	//  调用 DAO 层函数执行数据库操作
	err := dao_cdk.CreateCDKBatchWithRecords(ctx, cdkBatch, cdkRecords)
	if err != nil {
		entry.Errorf("CreateCDKBatchWithCodes: failed to create CDK batch and records via DAO: %v", err)
		return fmt.Errorf("failed to create CDK batch and associated codes: %w", err)
	}

	output.Batch = cdkBatch
	entry.Infof("CreateCDKBatchWithCodes: successfully created CDK batch ID %d with %d codes via DAO.", cdkBatch.ID, len(cdkCodesToCreate))
	return output, nil
}

// QueryCDKBatches CDK批次查询接口（分页）
func QueryCDKBatches(ctx context.Context, req *model_cdk.QueryCDKBatchesRequest) (*model_cdk.QueryCDKBatchesResponse, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("QueryCDKBatches request: %+v", req)

	// 参数验证
	if req.Page < 1 {
		return nil, errors.New("page must be greater than 0")
	}
	if req.PageSize < 1 || req.PageSize > 1000 {
		return nil, errors.New("page_size must be between 1 and 1000")
	}
	if req.Status != nil && (*req.Status < model_cdk.CDKStatusValid || *req.Status > model_cdk.CDKStatusInvalid) {
		return nil, errors.New("invalid status value")
	}

	// 调用 DAO 层查询
	batches, total, err := dao_cdk.QueryCDKBatches(ctx, req.Page, req.PageSize, req.Status)
	if err != nil {
		entry.Errorf("QueryCDKBatches: failed to query batches: %v", err)
		return nil, fmt.Errorf("failed to query CDK batches: %w", err)
	}

	response := &model_cdk.QueryCDKBatchesResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Batches:  batches,
	}

	entry.Infof("QueryCDKBatches: successfully queried %d batches, total=%d", len(batches), total)
	return response, nil
}

// QueryCDKRecords CDK记录查看接口
func QueryCDKRecords(ctx context.Context, req *model_cdk.QueryCDKRecordsRequest) (*model_cdk.QueryCDKRecordsResponse, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("QueryCDKRecords request: %+v", req)

	// 参数验证
	if req.BatchID == 0 {
		return nil, errors.New("batch_id is required")
	}
	if req.Page < 1 {
		return nil, errors.New("page must be greater than 0")
	}
	if req.PageSize < 1 || req.PageSize > 1000 {
		return nil, errors.New("page_size must be between 1 and 1000")
	}

	// 调用 DAO 层查询
	records, total, err := dao_cdk.QueryCDKRecords(ctx, req.BatchID, req.Page, req.PageSize)
	if err != nil {
		entry.Errorf("QueryCDKRecords: failed to query records for batch %d: %v", req.BatchID, err)
		return nil, fmt.Errorf("failed to query CDK records: %w", err)
	}

	response := &model_cdk.QueryCDKRecordsResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Records:  records,
	}

	entry.Infof("QueryCDKRecords: successfully queried %d records for batch %d, total=%d", len(records), req.BatchID, total)
	return response, nil
}

// DisableCDKBatch CDK批次作废接口
func DisableCDKBatch(ctx context.Context, req *model_cdk.DisableCDKBatchRequest) (*model_cdk.DisableCDKBatchResponse, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("DisableCDKBatch request: %+v", req)

	// 参数验证
	if req.BatchID == 0 {
		return nil, errors.New("batch_id is required")
	}

	// 调用 DAO 层作废批次
	err := dao_cdk.DisableCDKBatch(ctx, req.BatchID)
	if err != nil {
		entry.Errorf("DisableCDKBatch: failed to disable batch %d: %v", req.BatchID, err)
		return &model_cdk.DisableCDKBatchResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to disable CDK batch: %v", err),
		}, nil
	}

	response := &model_cdk.DisableCDKBatchResponse{
		Success: true,
		Message: fmt.Sprintf("CDK batch %d has been successfully disabled", req.BatchID),
	}

	entry.Infof("DisableCDKBatch: successfully disabled batch %d", req.BatchID)
	return response, nil
}
