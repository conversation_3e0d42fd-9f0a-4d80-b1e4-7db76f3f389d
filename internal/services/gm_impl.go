package services

import (
	"context"
	"sync"

	"google.golang.org/protobuf/encoding/protojson"

	"hallsrv/internal/logic/logic_cdk"
	logicItem "hallsrv/internal/logic/logic_item"
	"hallsrv/internal/repository"

	"encoding/json"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

type GmService struct{}

var (
	gmOnce              = &sync.Once{}
	gmSingletonInstance *GmService
)

func GetGmInstance() *GmService {
	if gmSingletonInstance != nil {
		return gmSingletonInstance
	}
	gmOnce.Do(func() {
		gmSingletonInstance = &GmService{}
	})
	return gmSingletonInstance
}

func (s *GmService) OperatePlayerItem(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:optItem]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdOperateItemReq{}
	opt := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err := opt.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}

	if reqd.GetItemId() <= 0 || reqd.GetItemCount() <= 0 || reqd.GetPlayerId() <= 0 {
		entry.Errorf("[gm:optItem] invalid param:%s", reqd.String())
		return rsp, nil
	}

	// 消耗道具信息
	optItemBase := []*commonPB.ItemBase{
		{
			ItemId:    reqd.GetItemId(),
			ItemCount: reqd.GetItemCount(),
		},
	}

	// 调用道具操作接口
	rewordInfo, err := logicItem.OperatePlayerItem(ctx, reqd.GetPlayerId(), optItemBase, reqd.GetItemOperation(), commonPB.ITEM_SOURCE_TYPE_IST_GM_OPERATION, commonPB.STORAGE_TYPE_ST_STORE, reqd.GetIsUnpack())
	if err != nil || rewordInfo == nil {
		entry.Errorf("[gm:optItem] operatePlayerItem, req:%s, err:%s", reqd.String(), err)
		return rsp, nil
	}

	// 返回结果
	rspData := &gmPB.GmCmdOperateItemRsp{
		RewardInfo: rewordInfo,
	}

	js, _ := gm_handler.Marshal(rspData)
	rsp.Data = string(js)

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("gm:optItem:req:%s, rsp:%s", reqd.String(), rspData.String())

	return rsp, nil
}

func (s *GmService) OperateContinuousLogin(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:optContinuousLogin]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdContinuousLoginReq{}
	gm_handler.Unmarshal([]byte(req.Data), reqd)

	if reqd.GetPlayerId() <= 0 {
		entry.Errorf("[gm:optContinuousLogin] invalid param:%s", reqd.String())
		return rsp, nil
	}
	// 清理数据
	if reqd.IsClear > 0 {
		err := repository.ClearContinuousLogin(ctx, reqd.GetPlayerId())
		if err != nil {
			entry.Errorf("[gm:optContinuousLogin] clearContinuousLogin, req:%s, err:%s", reqd.String(), err)
			return rsp, nil
		}
	} else {
		// 更新日期
		err := repository.UpdateContinuousLoginByGmTs(ctx, reqd.GetPlayerId(), reqd.GetLastUpdate())
		if err != nil {
			entry.Errorf("[gm:optContinuousLogin] updateContinuousLogin, req:%s, err:%s", reqd.String(), err)
			return rsp, nil
		}
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Debugf("gm:optContinuousLogin:req:%s, rsp:%s", reqd.String(), rsp.String())

	return rsp, nil

}

func (s *GmService) CreateCDKBatchWithCodes(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:CreateCDKBatchWithCodes]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	gmReq := &gmPB.GmCmdCreateCDKReq{}

	err := json.Unmarshal([]byte(req.Data), gmReq)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	err = logic_cdk.CreateCDKBatchWithCodes(ctx, gmReq)
	if err != nil {
		rsp.Ret = protox.FillErrResult(err)
		return rsp, nil
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}
